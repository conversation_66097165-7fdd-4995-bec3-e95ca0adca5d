    <#
    Title: Check employees without an all-staff distro group.
    DESC: This script checks employees at the company for a group with "All Staff" In it. Those without that group are highlighted.
    Prereqs:
    install-module exchangeonline
    install-module azuread

    #>
    
    #First, lets connect to exchange and azure
    Write-Host "Connecting to Exchange Online..."
    Connect-ExchangeOnline
    Write-Host "Connecting to Azure AD..."
    Connect-AzureAD

    #next we will make sure our objects are empty.
    write-host "Clearing Objects"
    $IsNotInAllStaff =@()
    $inAllStaff = @()
    
    $userList = $null
    $MemberOfAllStaff = $null


    #Now we can collect a list of users to evaluate. 
    Write-Host "Collecting userlist..."
    <#
    Userlist filters out service accounts, domain admins, test accounts and svc. This includes QHR and external employees. Later on, the check compares externals as well by including the All staff external mailing list. To remove externals, add the following line to the user list generation filter.
    -and Company -like "*QHR*"
    #>
    try{
    $userList =  Get-aduser -filter 'Title -ne "Service Account" -and Name -notlike "*Domain*" -and Name -notlike "*test*" -and Name -notlike "*svc*" ' | select UserPrincipalName
    }
    catch{
        Write-Error "Please run this script from QTADMIN1"
    }
    Write-Host "User List Generated"
    
    Write-host "Generating All Staff group list"
    #This next command gets all the all staff distribution list displaynames and email addresses.
    $allStaffGroups = Get-EXORecipient -RecipientType MailUniversalDistributionGroup -Filter 'PrimarySmtpAddress -like "*qhrallstaff*"' | select DisplayName, PrimarySmtpAddress, ExternalDirectoryObjectId
    Write-Host "List Generated, collecting members of the groups..."

    #This next command retrieves all members of all staff groups and adds them to a list
    ForEach($group in $allStaffGroups)
       {
        $MemberOfAllStaff += Get-AzureADGroupMember -ObjectId $group.ExternalDirectoryObjectId -All $true | Select UserPrincipalName
        }
        $AllStaffMembers = @()
   
   # I had to convert the members in the memberofallstaff array to this other array becuase the object property name "displayname" prevents the comparison operators in the comparison below from working.
    ForEach($member in $MemberOfAllStaff)
        {
            $AllStaffMembers+= $member.UserPrincipalName
        }

    #This next command compares the all staff member list with the user list generated earlier. Any not contained in the all staff member list are noted.
    ForEach($Employee in $userList)
    {
        if($AllStaffMembers -ne $null)
        {     
            if($AllStaffMembers -contains $Employee.UserPrincipalName)
            {
                write-host "Employee $Employee is in an all staff group"
                $inAllStaff += $Employee.UserPrincipalName
                continue
            }
            else{
                $IsNotInAllStaff += $Employee.UserPrincipalName
                Write-Host "$Employee Is not apart of an All staff group"
            }
        }
    }
        Write-Host "The following Employees have no all staff group"
        $IsNotInAllStaff
        Write-host "Now Disconnecting"
        Disconnect-ExchangeOnline
        Disconnect-AzureAD